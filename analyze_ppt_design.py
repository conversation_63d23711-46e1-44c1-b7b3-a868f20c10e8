#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联劝公益PPT视觉设计分析工具
分析PPT页面的色彩、布局、字体等视觉元素
"""

import os
import glob
from PIL import Image, ImageDraw, ImageFont
import numpy as np
from collections import Counter
import colorsys
import json

def rgb_to_hex(rgb):
    """将RGB值转换为十六进制颜色代码"""
    return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

def get_color_name(rgb):
    """根据RGB值推测颜色名称"""
    r, g, b = rgb
    
    # 定义常见颜色范围
    if r > 240 and g > 240 and b > 240:
        return "白色/浅灰"
    elif r < 50 and g < 50 and b < 50:
        return "黑色/深灰"
    elif r > 200 and g < 100 and b < 100:
        return "红色系"
    elif r < 100 and g > 200 and b < 100:
        return "绿色系"
    elif r < 100 and g < 100 and b > 200:
        return "蓝色系"
    elif r > 200 and g > 200 and b < 100:
        return "黄色系"
    elif r > 150 and g > 100 and b < 100:
        return "橙色系"
    elif r > 100 and g < 100 and b > 150:
        return "紫色系"
    elif 100 < r < 200 and 100 < g < 200 and 100 < b < 200:
        return "中性灰"
    else:
        return "其他色彩"

def analyze_image_colors(image_path, top_n=8):
    """分析图片的主要颜色"""
    try:
        img = Image.open(image_path)
        img = img.convert('RGB')
        
        # 获取图片尺寸
        width, height = img.size
        
        # 缩小图片以提高处理速度
        img_small = img.resize((width//4, height//4), Image.Resampling.LANCZOS)
        pixels = list(img_small.getdata())
        
        # 统计颜色使用频率
        color_counts = Counter(pixels)
        most_common_colors = color_counts.most_common(top_n)
        
        total_pixels = len(pixels)
        color_analysis = []
        
        for color, count in most_common_colors:
            percentage = (count / total_pixels) * 100
            hex_color = rgb_to_hex(color)
            color_name = get_color_name(color)
            
            color_analysis.append({
                'rgb': color,
                'hex': hex_color,
                'percentage': round(percentage, 2),
                'color_name': color_name,
                'count': count
            })
        
        return {
            'dimensions': (width, height),
            'colors': color_analysis
        }
    except Exception as e:
        print(f'分析图片 {image_path} 时出错: {e}')
        return None

def detect_layout_structure(image_path):
    """检测页面布局结构"""
    try:
        img = Image.open(image_path)
        img_gray = img.convert('L')
        img_array = np.array(img_gray)
        
        height, width = img_array.shape
        
        # 检测水平和垂直的空白区域
        horizontal_profile = np.mean(img_array, axis=1)
        vertical_profile = np.mean(img_array, axis=0)
        
        # 计算边距（基于亮度变化）
        threshold = np.mean(img_array) * 0.95
        
        # 上边距
        top_margin = 0
        for i, val in enumerate(horizontal_profile):
            if val < threshold:
                top_margin = i
                break
        
        # 下边距
        bottom_margin = height
        for i, val in enumerate(reversed(horizontal_profile)):
            if val < threshold:
                bottom_margin = height - i
                break
        
        # 左边距
        left_margin = 0
        for i, val in enumerate(vertical_profile):
            if val < threshold:
                left_margin = i
                break
        
        # 右边距
        right_margin = width
        for i, val in enumerate(reversed(vertical_profile)):
            if val < threshold:
                right_margin = width - i
                break
        
        return {
            'margins': {
                'top': top_margin,
                'bottom': height - bottom_margin,
                'left': left_margin,
                'right': width - right_margin
            },
            'content_area': {
                'width': right_margin - left_margin,
                'height': bottom_margin - top_margin
            }
        }
    except Exception as e:
        print(f'分析布局 {image_path} 时出错: {e}')
        return None

def analyze_all_slides():
    """分析所有PPT页面"""
    slide_files = sorted(glob.glob('slide*.png'))
    
    if not slide_files:
        print("未找到PPT页面图片文件")
        return
    
    print(f"找到 {len(slide_files)} 个PPT页面")
    
    all_colors = []
    layout_patterns = []
    
    # 分析每一页
    for i, slide_file in enumerate(slide_files):
        print(f"\n=== 分析第 {i+1} 页: {slide_file} ===")
        
        # 颜色分析
        color_analysis = analyze_image_colors(slide_file)
        if color_analysis:
            print(f"页面尺寸: {color_analysis['dimensions'][0]} x {color_analysis['dimensions'][1]}")
            print("主要颜色:")
            for j, color_info in enumerate(color_analysis['colors']):
                print(f"  {j+1}. {color_info['hex']} ({color_info['color_name']}) - {color_info['percentage']}%")
            
            all_colors.extend(color_analysis['colors'])
        
        # 布局分析
        layout_analysis = detect_layout_structure(slide_file)
        if layout_analysis:
            margins = layout_analysis['margins']
            print(f"页面边距: 上{margins['top']}px, 下{margins['bottom']}px, 左{margins['left']}px, 右{margins['right']}px")
            layout_patterns.append(layout_analysis)
        
        # 只详细分析前10页，避免输出过长
        if i >= 9:
            print(f"... (继续分析剩余 {len(slide_files) - 10} 页)")
            break
    
    # 汇总分析
    print("\n" + "="*50)
    print("整体设计规范汇总")
    print("="*50)
    
    # 汇总颜色使用
    color_summary = {}
    for color_info in all_colors:
        hex_color = color_info['hex']
        if hex_color in color_summary:
            color_summary[hex_color]['count'] += color_info['count']
            color_summary[hex_color]['pages'] += 1
        else:
            color_summary[hex_color] = {
                'rgb': color_info['rgb'],
                'color_name': color_info['color_name'],
                'count': color_info['count'],
                'pages': 1
            }
    
    # 按使用频率排序
    sorted_colors = sorted(color_summary.items(), key=lambda x: x[1]['count'], reverse=True)
    
    print("\n主要色彩方案:")
    for i, (hex_color, info) in enumerate(sorted_colors[:10]):
        print(f"  {i+1}. {hex_color} ({info['color_name']}) - 出现在 {info['pages']} 页")
    
    # 汇总布局模式
    if layout_patterns:
        avg_margins = {
            'top': np.mean([p['margins']['top'] for p in layout_patterns]),
            'bottom': np.mean([p['margins']['bottom'] for p in layout_patterns]),
            'left': np.mean([p['margins']['left'] for p in layout_patterns]),
            'right': np.mean([p['margins']['right'] for p in layout_patterns])
        }
        
        print(f"\n平均页面边距:")
        print(f"  上边距: {avg_margins['top']:.0f}px")
        print(f"  下边距: {avg_margins['bottom']:.0f}px") 
        print(f"  左边距: {avg_margins['left']:.0f}px")
        print(f"  右边距: {avg_margins['right']:.0f}px")
    
    # 保存分析结果
    analysis_result = {
        'total_slides': len(slide_files),
        'color_palette': sorted_colors[:15],
        'layout_patterns': layout_patterns,
        'average_margins': avg_margins if layout_patterns else None
    }
    
    with open('ppt_design_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析结果已保存到 ppt_design_analysis.json")

if __name__ == "__main__":
    analyze_all_slides()
